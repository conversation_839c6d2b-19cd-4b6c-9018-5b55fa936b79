#!/usr/bin/env python3
"""
图片重命名脚本
将img目录下的所有图片按顺序重命名为001, 002, 003...格式
保持原有扩展名不变
"""

from pathlib import Path


def get_image_files(directory):
    """获取目录下所有图片文件"""
    # 支持的图片格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    
    image_files = []
    directory_path = Path(directory)
    
    if not directory_path.exists():
        print(f"错误: 目录 '{directory}' 不存在")
        return []
    
    for file_path in directory_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(file_path)
    
    return image_files


def rename_images(img_directory="qu_images", start_number=1, dry_run=False):
    """
    重命名图片文件 - 使用两阶段重命名避免文件名冲突
    将所有扩展名统一改为.jpg

    Args:
        img_directory: 图片目录路径
        start_number: 起始编号
        dry_run: 是否只预览不实际重命名
    """

    # 获取所有图片文件
    image_files = get_image_files(img_directory)

    if not image_files:
        print(f"在目录 '{img_directory}' 中没有找到图片文件")
        return

    # 按文件名排序
    image_files.sort(key=lambda x: x.name.lower())

    print(f"找到 {len(image_files)} 个图片文件")
    print(f"{'预览模式' if dry_run else '开始重命名'}:")
    print("-" * 50)

    # 准备重命名映射
    rename_mapping = []
    for i, old_file_path in enumerate(image_files):
        new_number = start_number + i
        new_filename = f"so_{new_number:04d}.jpg"  # 统一使用.jpg扩展名

        # 如果新文件名和旧文件名相同，跳过
        if old_file_path.name == new_filename:
            print(f"跳过: {old_file_path.name} (已经是正确格式)")
            continue

        rename_mapping.append((old_file_path, new_filename))
        print(f"{old_file_path.name} -> {new_filename}")

    print("-" * 50)

    if dry_run:
        print(f"预览完成，将重命名 {len(rename_mapping)} 个文件")
        print("运行时添加 --execute 参数来实际执行重命名")
        return

    if not rename_mapping:
        print("没有需要重命名的文件")
        return

    # 实际执行重命名 - 使用两阶段重命名避免冲突
    print("第一阶段: 将文件重命名为临时名称...")
    temp_mapping = []

    for old_file_path, new_filename in rename_mapping:
        # 生成临时文件名
        temp_filename = f"temp_{hash(old_file_path.name) % 100000}_.jpg"
        temp_file_path = old_file_path.parent / temp_filename

        try:
            old_file_path.rename(temp_file_path)
            temp_mapping.append((temp_file_path, new_filename))
            print(f"  {old_file_path.name} -> {temp_filename}")
        except Exception as e:
            print(f"错误: 第一阶段重命名 {old_file_path.name} 失败: {e}")
            continue

    print("\n第二阶段: 将临时文件重命名为最终名称...")
    renamed_count = 0

    for temp_file_path, new_filename in temp_mapping:
        new_file_path = temp_file_path.parent / new_filename

        try:
            temp_file_path.rename(new_file_path)
            print(f"  {temp_file_path.name} -> {new_filename}")
            renamed_count += 1
        except Exception as e:
            print(f"错误: 第二阶段重命名 {temp_file_path.name} 失败: {e}")
            # 尝试恢复原始文件名
            try:
                # 这里需要找到原始文件名，但由于我们已经在第一阶段改名了，
                # 所以只能保持临时名称
                print(f"  文件保持临时名称: {temp_file_path.name}")
            except:
                pass

    print("-" * 50)
    print(f"重命名完成，成功重命名了 {renamed_count} 个文件")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="批量重命名图片文件")
    parser.add_argument("--directory", "-d", default="qu_images",
                       help="图片目录路径 (默认: qu_images)")
    parser.add_argument("--start", "-s", type=int, default=1,
                       help="起始编号 (默认: 1)")
    parser.add_argument("--execute", action="store_true",
                       help="实际执行重命名 (默认只预览)")
    
    args = parser.parse_args()
    
    # 确认操作
    if args.execute:
        response = input(f"确定要重命名目录 '{args.directory}' 中的图片文件吗? (y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            return
    
    # 执行重命名
    rename_images(
        img_directory=args.directory,
        start_number=args.start,
        dry_run=not args.execute
    )


if __name__ == "__main__":
    main()
